import { Request, Response } from 'express';

import { createBrandApiValidator } from '../api_validators/brands-api-validators.js';
import { asyncHandler } from '../helpers/asyncHandler.js';
import Brand, { BrandDocumentResult } from '../models/BrandModel/BrandModel.js';

export const createBrand = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as { name: string };
  const { name } = body;

  const { error } = createBrandApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }
  const newBrand = new Brand({ name });
  await newBrand.save();

  return res.status(201).json({
    status: 'success',
    message: 'New brand created Successfully',
    data: newBrand
  });
});

export const getBrand = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const brand = await Brand.findOne<BrandDocumentResult>({ _id: id });
  if (!brand) {
    return res.status(404).json({
      status: 'success',
      message: `Brand not found with id ${id}`
    });
  }

  return res.status(200).json({
    status: 'success',
    data: brand
  });
});

export const updateBrand = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as { name: string };
  const { id } = req.params;
  const { name } = body;
  const { error } = createBrandApiValidator.validate({ ...req.body });
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const car = await Brand.findOneActive({ _id: id });
  if (!car) {
    return res.status(404).json({ error: `Brand not found with the id ${id} provided` });
  }

  const updatedBrand = await Brand.findOneAndUpdate<BrandDocumentResult>({ _id: id }, { name }, { new: true });

  return res.status(200).json({
    status: 'success',
    message: 'Brand Updated Successfully.',
    data: updatedBrand
  });
});

export const deleteBrand = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const car = await Brand.findOneActive({ _id: id });
  if (!car) {
    return res.status(404).json({ error: `brand not found with the id ${id} provided` });
  }

  const inactiveBrand = await Brand.findOneAndUpdate<BrandDocumentResult>(
    { _id: id },
    { is_active: false },
    { new: true }
  );

  return res.status(200).json({
    status: 'success',
    message: 'Brand deactivated Successfully.',
    data: inactiveBrand
  });
});

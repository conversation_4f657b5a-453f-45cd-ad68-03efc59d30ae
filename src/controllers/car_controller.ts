import { Request, Response } from 'express';
import mongoose, { Document } from 'mongoose';

import { createBrandApiValidator } from '../api_validators/brands-api-validators.js';
import {
  createCarApiValidator,
  deleteCarImagesApiValidator,
  fetchCarsApiValidator,
  updateCarApiValidator,
  updateMainImageApiValidator,
  updateVisibleCarImages
} from '../api_validators/car-api-validators.js';
import { addPointsToAddress } from '../helpers/application_helper.js';
import { asyncHandler } from '../helpers/asyncHandler.js';
import { getCurrentTimeInTimezone, isCarAvailableBySchedule } from '../helpers/car_helper.js';
import { manageFileUpload } from '../helpers/file_upload.js';
import { advancedResults } from '../helpers/query.js';
import Brand from '../models/BrandModel/BrandModel.js';
import CarCategory, { CarCategoryDocumentResult } from '../models/CarCategoryModel/CarCategoryModel.js';
import CarImage from '../models/CarImageModel/CarImageModel.js';
import Car, { CarDocumentResult } from '../models/CarModel/CarModel.js';
import Company from '../models/CompanyModel/CompanyModel.js';
import { CarDoc, RegisterCarRequestBody, UpdateCarRequestBody } from '../types/car.js';
import { AddressDoc, UserDoc } from '../types/user.js';
import { FetchCarsRequestBody } from './../types/car.js';

export const createCategory = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as { name: string; picturUrl: string };
  const { name, picturUrl } = body;

  const { error } = createBrandApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }
  const newCategory = new CarCategory({ name, picturUrl });
  await newCategory.save();

  return res.status(201).json({
    status: 'success',
    message: 'New Category created Successfully',
    data: newCategory
  });
});

export const getBrandsAndCategories = asyncHandler(async (req: Request, res: Response) => {
  const categories = await CarCategory.find({});
  const brands = await Brand.find({});
  return res.status(200).json({
    status: 'success',
    data: { categories, brands }
  });
});

export const updateCarCategory = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as { name: string };
  const { id } = req.params;
  const { name } = body;
  const { error } = createBrandApiValidator.validate({ ...req.body });
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const car = await CarCategory.findOneActive({ _id: id });
  if (!car) {
    return res.status(404).json({ error: `Category not found with the id ${id} provided` });
  }

  const updatedCategory = await CarCategory.findOneAndUpdate<CarCategoryDocumentResult>(
    { _id: id },
    { name },
    { new: true }
  );

  return res.status(200).json({
    status: 'success',
    message: 'Category Updated Successfully.',
    data: updatedCategory
  });
});

export const deleteCarCategory = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  if (!id) {
    return res.status(422).json({ error: 'id is required' });
  }

  const car = await CarCategory.findOneActive({ _id: id });
  if (!car) {
    return res.status(404).json({ error: `category not found with the id ${id} provided` });
  }

  const inactiveBrand = await CarCategory.findOneAndUpdate<CarCategoryDocumentResult>(
    { _id: id },
    { is_active: false },
    { new: true }
  );

  return res.status(200).json({
    status: 'success',
    message: 'Category deactivated Successfully.',
    data: inactiveBrand
  });
});

export const createCar = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as RegisterCarRequestBody;
  const { year, brandId, model, categoryId, address, dailyPrice, dailyMinPrice, schedule } = body;

  const { error } = createCarApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }
  const user = req.user as UserDoc;

  const company = await Company.findOne({ user: user._id });
  const updateAddress = addPointsToAddress(address);

  const newCar = new Car({
    year,
    brand: brandId,
    model,
    category: categoryId,
    address: updateAddress,
    dailyPrice,
    dailyMinPrice,
    schedule,
    createdBy: user._id,
    company: company._id
  });
  await newCar.save();
  await newCar.populate({ path: 'brand', select: 'name' });
  await newCar.populate({ path: 'category', select: 'name pictureUrl' });

  return res.status(201).json({
    status: 'success',
    message: 'New Car created Successfully',
    data: newCar
  });
});

export const getCars = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user as UserDoc;
  const company = user.company.toString();

  // Create a new URL with the createdBy parameter
  const url = new URL(req.url, 'http://localhost');
  url.searchParams.set('createdBy', user._id.toString());

  const cars = await advancedResults<CarDoc, CarDocumentResult & Document>(url.toString(), Car, company);
  await Car.populate(cars.results, {
    path: 'carImages',
    match: { isActive: true, isVisible: true }, // Only active and visible car images
    select: 'url isMain carId isVisible'
  });
  await Car.populate(cars.results, { path: 'brand', select: 'name' });
  await Car.populate(cars.results, { path: 'category', select: 'name pictureUrl' });
  await Car.populate(cars, { path: 'rating', select: 'comment rating' });

  return res.status(200).json({
    status: 'success',
    data: cars
  });
});

export const uploadCarImages = asyncHandler(async (req: Request, res: Response) => {
  if (!req.files) {
    return res.status(422).json({ message: 'kindly upload a file' });
  }
  const { carId, mainImageId } = req.body as { carId: string; mainImageId: string };
  const car = await Car.findOne({ _id: carId });
  const user = req.user as UserDoc;
  if (!car) {
    return res.status(404).json({
      status: 'failed',
      message: 'Car Not Found',
      data: car
    });
  }
  const fileData = {
    mainImageId,
    _id: car._id,
    company: car.company,
    createdBy: user._id
  };
  if (car) {
    const files = Array.isArray(req.files) ? req.files : req.file ? [req.file] : [];
    for (const file of files) {
      const { path, filename, originalname } = file;
      await manageFileUpload(path, filename, originalname, fileData);
    }
  }

  return res.status(200).json({
    status: 'success',
    message: 'Car Images Uploaded Successfully'
  });
});

export const updateImageVisibility = (req: Request, res: Response) => {
  const { error } = updateVisibleCarImages.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }
  const body = req.body as { imageId: string; isVisible: boolean }[];

  if (!body.length) {
    return res.status(404).json({
      status: 'failed',
      message: 'No Car Images to Update.'
    });
  }

  body.forEach(async element => {
    await CarImage.findOneAndUpdate(
      { _id: element.imageId },
      {
        isVisible: element.isVisible
      }
    );
  });

  return res.status(200).json({
    status: 'success',
    message: 'Car Images Updated Successfully'
  });
};

export const getCar = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({
      status: 'failed',
      message: 'Car ID is required'
    });
  }

  const pipeline = [
    {
      $match: {
        _id: new mongoose.Types.ObjectId(id),
        isActive: true
      }
    },
    {
      $lookup: {
        from: 'carimages',
        localField: '_id',
        foreignField: 'carId',
        as: 'carImages',
        pipeline: [
          { $match: { isActive: true, isVisible: true } },
          { $project: { url: 1, isMain: 1, carId: 1, isVisible: 1 } }
        ]
      }
    },
    {
      $lookup: {
        from: 'brands',
        localField: 'brand',
        foreignField: '_id',
        as: 'brand'
      }
    },
    {
      $unwind: {
        path: '$brand',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $lookup: {
        from: 'companies',
        localField: 'company',
        foreignField: '_id',
        as: 'company'
      }
    },
    {
      $unwind: {
        path: '$company',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $lookup: {
        from: 'ratings',
        localField: 'rating',
        foreignField: '_id',
        as: 'rating'
      }
    },
    {
      $unwind: {
        path: '$rating',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $addFields: {
        category: {
          $cond: {
            if: { $not: { $isArray: '$category' } },
            then: { $toObjectId: '$category' },
            else: '$category'
          }
        }
      }
    },
    {
      $lookup: {
        from: 'carcategories',
        localField: 'category',
        foreignField: '_id',
        as: 'category'
      }
    },
    {
      $unwind: {
        path: '$category',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $project: {
        _id: 1,
        description: 1,
        engineType: 1,
        schedule: 1,
        address: 1,
        year: 1,
        dailyPrice: 1,
        dailyMinPrice: 1,
        model: 1,
        ratingSummary: 1,
        brand: '$brand.name',
        company: '$company.name',
        rating: {
          rating: '$rating.rating',
          comment: '$rating.comment'
        },
        carImages: 1,
        bookingCount: 1,
        totalEarned: 1,
        bookedDates: 1,
        isAvailable: 1,
        createdAt: 1,
        updatedAt: 1,
        category: {
          _id: '$category._id',
          name: '$category.name',
          pictureUrl: '$category.pictureUrl'
        }
      }
    }
  ];

  const result = await Car.aggregate(pipeline);

  if (!result.length) {
    return res.status(404).json({
      status: 'failed',
      message: 'Car not found'
    });
  }

  return res.status(200).json({
    status: 'success',
    data: result[0]
  });
});

//  <<<switched from this to the aggregate method above>>>

// export const getCar = asyncHandler(async (req: Request, res: Response) => {
//   const { id } = req.params;
//   const user = req.user as UserDoc;
//   const car = await Car.findOneActive({ _id: id, company: user.company });
//   if (!car) {
//     return res.status(404).json({
//       status: 'failed',
//       message: 'car not found'
//     });
//   }
//   await Car.populate(car, {
//     path: 'carImages',
//     match: { isActive: true, isVisible: true },
//     select: 'url isMain carId isVisible'
//   });
//   await Car.populate(car, { path: 'brand', select: 'name' });
//   await Car.populate(car, { path: 'category', select: 'name pictureUrl' });
//   await Car.populate(car, { path: 'company', select: 'name' });
//   await Car.populate(car, { path: 'rating', select: 'comment rating' });

//   return res.status(200).json({
//     status: 'success',
//     data: car
//   });
// });

export const updateCar = asyncHandler(async (req: Request, res: Response) => {
  const { error } = updateCarApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const { id } = req.params;
  const user = req.user as UserDoc;
  const body = req.body as UpdateCarRequestBody;
  const { description, address, dailyPrice, dailyMinPrice, schedule, isAvailable } = body;

  if (!id) {
    return res.status(400).json({
      status: 'failed',
      message: 'Car ID is required'
    });
  }

  const car = await Car.findOneActive({ _id: id });
  if (!car) {
    return res.status(404).json({
      status: 'failed',
      message: 'Car not found'
    });
  }

  const updateAddress: AddressDoc = address?.longitude ? addPointsToAddress(address) : undefined;

  const updatedCar = await Car.findOneAndUpdate(
    { _id: id },
    {
      description,
      address: updateAddress,
      dailyPrice,
      dailyMinPrice,
      schedule,
      isAvailable,
      updatedBy: user._id
    },
    { new: true }
  );

  const pipeline = [
    {
      $match: {
        _id: updatedCar._id
      }
    },
    {
      $lookup: {
        from: 'carimages',
        localField: '_id',
        foreignField: 'carId',
        as: 'carImages',
        pipeline: [
          { $match: { isActive: true, isVisible: true } },
          { $project: { url: 1, isMain: 1, carId: 1, isVisible: 1 } }
        ]
      }
    },
    {
      $lookup: {
        from: 'brands',
        localField: 'brand',
        foreignField: '_id',
        as: 'brand'
      }
    },
    {
      $unwind: {
        path: '$brand',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $lookup: {
        from: 'carcategories',
        localField: 'category',
        foreignField: '_id',
        as: 'category'
      }
    },
    {
      $unwind: {
        path: '$category',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $project: {
        _id: 1,
        description: 1,
        engineType: 1,
        schedule: 1,
        address: 1,
        year: 1,
        dailyPrice: 1,
        dailyMinPrice: 1,
        model: 1,
        brand: '$brand.name',
        category: {
          _id: '$category._id',
          name: '$category.name',
          pictureUrl: '$category.pictureUrl'
        },
        carImages: 1,
        isAvailable: 1,
        createdAt: 1,
        updatedAt: 1
      }
    }
  ];

  const result = await Car.aggregate(pipeline);

  return res.status(200).json({
    status: 'success',
    data: result[0]
  });
});

//  <<<switched from this to the aggregate method above>>>

// export const updateCar = asyncHandler(async (req: Request, res: Response) => {
//   const { error } = updateCarApiValidator.validate(req.body);
//   if (error) {
//     return res.status(422).json({ error: error.details[0].message });
//   }
//   const { id } = req.params;
//   const user = req.user as UserDoc;
//   const body = req.body as UpdateCarRequestBody;
//   const { description, address, dailyPrice, dailyMinPrice, schedule, isAvailable } = body;
//   const car = await Car.findOneActive({ _id: id });
//   if (!car) {
//     return res.status(404).json({
//       status: 'failed',
//       message: 'car not found'
//     });
//   }

//   const updateAddress: AddressDoc = address?.longitude ? addPointsToAddress(address) : undefined;

//   const updatedCar = await Car.findOneAndUpdate(
//     { _id: id },
//     {
//       description,
//       address: updateAddress,
//       dailyPrice,
//       dailyMinPrice,
//       schedule,
//       isAvailable,
//       updatedBy: user._id
//     },
//     { new: true }
//   );

//   await Car.populate(updatedCar, {
//     path: 'carImages',
//     match: { isVisible: true },
//     select: 'url isMain carId isVisible'
//   });
//   await Car.populate(updatedCar, { path: 'brand', select: 'name' });
//   await Car.populate(updatedCar, { path: 'category', select: 'name pictureUrl' });
//   await Car.populate(updatedCar, { path: 'company', select: 'name' });

//   return res.status(200).json({
//     status: 'success',
//     data: updatedCar
//   });
// });

export const delistCar = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const car = await Car.findActive({ _id: id });
  if (!car) {
    return res.status(404).json({
      status: 'failed',
      message: 'car not found'
    });
  }

  await Car.findOneAndUpdate(
    { _id: id },
    {
      isActive: false
    }
  );

  return res.status(200).json({
    status: 'success',
    message: 'car delisted successfully'
  });
});

export const deleteCarImages = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userCompanpany = req.user as UserDoc;
  const company = userCompanpany.company;
  const car = await Car.findActive({ _id: id, company });
  if (!car) {
    return res.status(404).json({
      status: 'failed',
      message: 'car not found'
    });
  }
  const { error } = deleteCarImagesApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }
  const body = req.body as { imageIds: string[] };
  const user = req.user as UserDoc;

  if (!body.imageIds.length) {
    return res.status(404).json({
      status: 'failed',
      message: 'No Car Images to Update.'
    });
  }

  body.imageIds.forEach(async id => {
    await CarImage.findOneAndUpdate(
      { _id: id, companyId: user.company },
      {
        isActive: false
      }
    );
  });

  return res.status(200).json({
    status: 'success',
    message: 'Car Images Deleted Successfully'
  });
});

export const updateMainImage = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const user = req.user as UserDoc;
  const { company } = user;
  const body = req.body as { imageId?: string };

  const car = await Car.findActive({ _id: id, company });
  if (!car) {
    return res.status(404).json({
      status: 'failed',
      message: 'Car not found'
    });
  }

  const { error } = updateMainImageApiValidator.validate(body);
  if (error) {
    return res.status(422).json({
      status: 'failed',
      message: error.details[0].message
    });
  }

  if (!body.imageId) {
    return res.status(400).json({
      status: 'failed',
      message: 'Image ID is required to update the car image.'
    });
  }

  const mainImage = await CarImage.findOne({ isMain: true, companyId: company });

  const updateTasks: Promise<unknown>[] = [];

  if (mainImage) {
    updateTasks.push(CarImage.findOneAndUpdate({ _id: mainImage._id, companyId: company }, { isMain: false }));
  }

  updateTasks.push(CarImage.findOneAndUpdate({ _id: body.imageId, companyId: company }, { isMain: true }));
  await Promise.all(updateTasks);

  return res.status(200).json({
    status: 'success',
    message: 'Car image updated successfully.'
  });
});

export const fetchCarsForClient = asyncHandler(async (req: Request, res: Response) => {
  const body = req.body as FetchCarsRequestBody;
  const { error } = fetchCarsApiValidator.validate(body);
  if (error) {
    return res.status(422).json({
      status: 'failed',
      message: error.details[0].message
    });
  }
  const { pickupAddress, destinationAddress, timezone, useScheduleFilter } = body;

  const pipeline = [
    {
      // Step 1: Match cars that are active and in the same state as pickup address
      $match: {
        isActive: true,
        'address.state': pickupAddress.state
      }
    },
    {
      // Step 2: Populate carImages for each car
      $lookup: {
        from: 'carimages', // Collection name for car images
        localField: '_id',
        foreignField: 'carId',
        as: 'carImages',
        pipeline: [
          { $match: { isActive: true, isVisible: true } }, // Only active and visible car images
          { $project: { url: 1, isMain: 1, carId: 1, isVisible: 1 } }
        ]
      }
    },

    {
      // Step 3: Populate brand field
      $lookup: {
        from: 'brands',
        localField: 'brand',
        foreignField: '_id',
        as: 'brand'
      }
    },
    {
      $unwind: {
        path: '$brand',
        preserveNullAndEmptyArrays: true // If no brand, leave it as null
      }
    },
    {
      // Step 4: Populate company field
      $lookup: {
        from: 'companies',
        localField: 'company',
        foreignField: '_id',
        as: 'company'
      }
    },
    {
      $unwind: {
        path: '$company',
        preserveNullAndEmptyArrays: true // If no company, leave it as null
      }
    },
    {
      $lookup: {
        from: 'ratings',
        localField: 'rating',
        foreignField: '_id',
        as: 'rating'
      }
    },
    {
      $unwind: {
        path: '$rating',
        preserveNullAndEmptyArrays: true // If no company, leave it as null
      }
    },
    {
      // Step 5: Cast category field to ObjectId if necessary
      $addFields: {
        category: {
          $cond: {
            if: { $not: { $isArray: '$category' } },
            then: { $toObjectId: '$category' }, // Cast to ObjectId if it's not an array
            else: '$category'
          }
        }
      }
    },
    {
      // Step 6: Populate category field
      $lookup: {
        from: 'carcategories', // Collection name for car categories
        localField: 'category',
        foreignField: '_id',
        as: 'category'
      }
    },
    {
      $unwind: {
        path: '$category',
        preserveNullAndEmptyArrays: true // If no category, leave it as null
      }
    },
    {
      // Step 7: Add calculated distances in kilometers using Haversine formula
      $addFields: {
        carToPickupDistance: {
          $let: {
            vars: {
              // Use car's address latitude and longitude fields directly
              carLat: '$address.latitude',
              carLng: '$address.longitude',
              pickupLat: pickupAddress.latitude,
              pickupLng: pickupAddress.longitude
            },
            in: {
              $multiply: [
                6371, // Earth's radius in km
                {
                  $acos: {
                    $add: [
                      {
                        $multiply: [
                          { $sin: { $multiply: [{ $divide: ['$$carLat', 57.2958] }, 1] } },
                          { $sin: { $multiply: [{ $divide: ['$$pickupLat', 57.2958] }, 1] } }
                        ]
                      },
                      {
                        $multiply: [
                          { $cos: { $multiply: [{ $divide: ['$$carLat', 57.2958] }, 1] } },
                          { $cos: { $multiply: [{ $divide: ['$$pickupLat', 57.2958] }, 1] } },
                          {
                            $cos: { $multiply: [{ $divide: [{ $subtract: ['$$pickupLng', '$$carLng'] }, 57.2958] }, 1] }
                          }
                        ]
                      }
                    ]
                  }
                }
              ]
            }
          }
        },
        pickupToDestinationDistance: {
          $let: {
            vars: {
              pickupLat: pickupAddress.latitude,
              pickupLng: pickupAddress.longitude,
              destLat: destinationAddress.latitude,
              destLng: destinationAddress.longitude
            },
            in: {
              $multiply: [
                6371, // Earth's radius in km
                {
                  $acos: {
                    $add: [
                      {
                        $multiply: [
                          { $sin: { $multiply: [{ $divide: ['$$pickupLat', 57.2958] }, 1] } },
                          { $sin: { $multiply: [{ $divide: ['$$destLat', 57.2958] }, 1] } }
                        ]
                      },
                      {
                        $multiply: [
                          { $cos: { $multiply: [{ $divide: ['$$pickupLat', 57.2958] }, 1] } },
                          { $cos: { $multiply: [{ $divide: ['$$destLat', 57.2958] }, 1] } },
                          {
                            $cos: {
                              $multiply: [{ $divide: [{ $subtract: ['$$destLng', '$$pickupLng'] }, 57.2958] }, 1]
                            }
                          }
                        ]
                      }
                    ]
                  }
                }
              ]
            }
          }
        }
      }
    },
    {
      $addFields: {
        totalDistance: { $add: ['$carToPickupDistance', '$pickupToDestinationDistance'] }
      }
    },
    {
      // Step 8: Group cars by their category
      $group: {
        _id: '$category._id',
        name: { $first: '$category.name' },
        imageUrl: { $first: '$category.pictureUrl' },
        createdAt: { $first: '$category.createdAt' },
        cars: {
          $push: {
            _id: '$_id',
            description: '$description',
            engineType: '$engineType',
            schedule: '$schedule',
            address: '$address',
            year: '$year',
            dailyPrice: '$dailyPrice',
            dailyMinPrice: '$dailyMinPrice',
            model: '$model',
            ratingSummary: '$ratingSummary',
            brand: '$brand.name',
            company: '$company.name',
            rating: {
              rating: '$rating.rating',
              comment: '$rating.comment'
            },
            carImages: '$carImages',
            bookingCount: '$bookingCount',
            totalEarned: '$totalEarned',
            bookedDates: '$bookedDates',
            isAvailable: '$isAvailable',
            createdAt: '$createdAt',
            updatedAt: '$updatedAt',
            // Add calculated distances
            carToPickupDistanceKm: { $round: ['$carToPickupDistance', 2] },
            pickupToDestinationDistanceKm: { $round: ['$pickupToDestinationDistance', 2] },
            totalDistanceKm: { $round: ['$totalDistance', 2] }
          }
        }
      }
    },
    {
      // Step 8: Format the final output
      $project: {
        id: '$_id',
        name: 1,
        imageUrl: 1,
        createdAt: 1,
        cars: 1
      }
    }
  ];
  // TODO: Add pagination and sorting to this endpoint
  const result = await Car.aggregate(pipeline);

  // Filter cars by schedule availability using server-side time
  if (useScheduleFilter !== false) {
    // Default to true if not specified
    result.forEach(category => {
      category.cars = category.cars.filter(car => isCarAvailableBySchedule(car.schedule, timezone));
    });

    // Remove categories with no available cars
    const filteredResult = result.filter(category => category.cars.length > 0);

    return res.status(200).json({
      status: 'success',
      data: filteredResult,
      meta: {
        scheduleFilterApplied: true,
        timezone: timezone || 'Africa/Lagos',
        serverTime: getCurrentTimeInTimezone(timezone)
      }
    });
  }

  return res.status(200).json({
    status: 'success',
    data: result,
    meta: {
      scheduleFilterApplied: false,
      timezone: timezone || 'Africa/Lagos',
      serverTime: getCurrentTimeInTimezone(timezone)
    }
  });
});

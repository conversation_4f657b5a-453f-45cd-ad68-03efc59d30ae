import { Request, Response } from 'express';
import { Document } from 'mongoose';
import { nanoid } from 'nanoid';

import {
  chargeMandateApiValidator,
  createTransactionApiValidator,
  processTransactionApiValidator
} from '../api_validators/transaction-api-validator.js';
import { asyncHandler } from '../helpers/asyncHandler.js';
import {
  initializePaystackTransaction,
  paystackChargeMandate,
  processPaystackTransaction,
  verifyPaystackTransaction
} from '../helpers/paystack_helper.js';
import { advancedResults } from '../helpers/query.js';
import Booking from '../models/BookingModel/BookingModel.js';
import Mandate from '../models/TransactionModel/MandateModel.js';
import Transaction, { TransactionDocumentResult } from '../models/TransactionModel/TransactionModel.js';
import { PaystackInitDocs, VerifyDoc } from '../types/paystack.js';
import { TRANSACTION_STATUS, TRANSACTION_TYPE, TransactionDoc } from '../types/transactions.js';

const REFERENCE_CHAR_LENGTH = 16;

export const createTransaction = asyncHandler(async (req: Request, res: Response) => {
  const { error } = createTransactionApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const body = req.body as { bookingId: string };
  const { bookingId } = body;
  const client = req.client;
  const booking = await Booking.findOne({ _id: bookingId });

  if (!booking) {
    return res.status(404).json({
      status: 'failed',
      message: 'No booking Found'
    });
  }

  const transaction = await Transaction.findOne({ bookingId });
  if (transaction?.status === TRANSACTION_STATUS.ATTEMPTED) {
    await Transaction.deleteOne({ bookingId });
  }

  const newTransaction = new Transaction({
    clientId: client._id,
    description: TRANSACTION_TYPE.BOOKING + ' transaction',
    bookingId: booking._id,
    email: client.email,
    currency: 'NGN',
    amount: booking.totalPrice,
    type: TRANSACTION_TYPE.BOOKING
  });

  await newTransaction.save();

  const paystackTransaction = (await initializePaystackTransaction(newTransaction)) as PaystackInitDocs;
  console.error(paystackTransaction);
  const { access_code, authorization_url, reference } = paystackTransaction.data;

  newTransaction.extra = paystackTransaction.data;
  newTransaction.checkoutUrl = authorization_url;
  newTransaction.auth = access_code;

  await newTransaction.save();
  await Booking.updateOne({ _id: booking._id }, { bookingReference: reference });
  await Transaction.updateOne({ _id: newTransaction._id }, { reference }, { new: true });

  return res.status(201).json({
    status: 'success',
    message: 'Transaction Created Successfully',
    data: newTransaction
  });
});

export const verifyTransaction = asyncHandler(async (req: Request, res: Response) => {
  const { error } = processTransactionApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const body = req.body as { reference: string; bookingId: string };
  const transaction = await Transaction.findOne({ bookingId: body.bookingId });

  if (!transaction) {
    return res.status(404).json({
      status: 'failed',
      message: 'Transaction not found'
    });
  }

  const verifyTransaction = (await verifyPaystackTransaction(body.reference)) as VerifyDoc;

  if (verifyTransaction?.status === 'success') {
    await processPaystackTransaction(transaction, verifyTransaction, body.reference);
    // TODO: send Email to Client about there bookings
  }

  return res.status(201).json({
    status: 'success',
    message: 'Transaction processed Successfully'
  });
});

export const chargeMandate = asyncHandler(async (req: Request, res: Response) => {
  const { error } = chargeMandateApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }
  const client = req.client;
  const body = req.body as { mandateId: string; bookingId: string };
  const { mandateId, bookingId } = body;
  const mandate = await Mandate.findOne({ _id: mandateId });
  const booking = await Booking.findOne({ _id: bookingId });

  if (!mandate) {
    return res.status(404).json({
      status: 'failed',
      message: 'Mandate not found'
    });
  }

  if (!booking) {
    return res.status(404).json({
      status: 'failed',
      message: 'Booking not found'
    });
  }

  const transaction = new Transaction({
    clientId: client._id,
    email: client.email,
    gateway: mandate.gateway,
    country: mandate.country,
    amount: booking.totalPrice,
    reference: nanoid(REFERENCE_CHAR_LENGTH),
    description: TRANSACTION_TYPE.BOOKING + ' transaction',
    type: TRANSACTION_TYPE.CHARGE_MANDATE
  });

  await transaction.save();

  const charge = paystackChargeMandate(transaction);

  return res.status(201).json({
    status: 'success',
    message: 'Transaction processed Successfully',
    data: charge
  });
});

export const removeMandate = asyncHandler(async (req: Request, res: Response) => {
  const { error } = chargeMandateApiValidator.validate(req.body);
  if (error) {
    return res.status(422).json({ error: error.details[0].message });
  }

  const body = req.body as { mandateId: string };
  const { mandateId } = body;
  const mandate = await Mandate.findByIdAndDelete({ _id: mandateId });

  if (!mandate) {
    return res.status(404).json({
      status: 'failed',
      message: 'Mandate not found'
    });
  }

  await Mandate.deleteOne({ _id: mandateId });

  return res.status(201).json({
    status: 'success',
    message: 'Mandate deleted Successfully'
  });
});

export const getMandates = asyncHandler(async (req: Request, res: Response) => {
  const client = req.client;
  const mandates = await Mandate.find({ clientId: client._id, reusable: true });

  return res.status(201).json({
    status: 'success',
    message: 'Mandate deleted Successfully',
    data: mandates
  });
});

/* export const paystackHook = asyncHandler(async (req: Request, res: Response) => {
  const {
    data: { reference }
  } = req.body as { data: { reference: string } };
  const transaction = await Transaction.findOne({ reference });

  if (transaction && transaction.status === TRANSACTION_STATUS.ATTEMPTED) {
    await processTransactionHook(transaction);
  }

  res.end();
}); */

export const getOneTransaction = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const transaction = await Transaction.findOne({ _id: id });
  if (!transaction) {
    return res.status(404).json({
      status: 'success',
      message: `Transaction not found with id ${id}`
    });
  }
  await Transaction.populate(transaction, { path: 'clientId' });

  return res.status(200).json({
    status: 'success',
    data: transaction
  });
});

export const getAllTransactions = asyncHandler(async (req: Request, res: Response) => {
  const transactions = await advancedResults<TransactionDoc, TransactionDocumentResult & Document>(
    req.url,
    Transaction
  );
  await Transaction.populate(transactions.results, {
    path: 'clientId'
  });

  return res.status(200).json({
    status: 'success',
    data: transactions
  });
});

import { emailTemplates } from '../consts.js';
// import Car from '../models/CarModel/CarModel.js'; // Unused import
import Logger from '../libs/logger.js';
import Booking from '../models/BookingModel/BookingModel.js';
import Client from '../models/ClientModel/ClientModel.js';
import { BOOKING_STATUS, BookingDoc } from '../types/booking.js';
import { TransactionDoc } from '../types/transactions.js';
import { sendMail } from './mail_helper.js';

export const sendTransactionReceiptEmail = async (transaction: TransactionDoc, booking: BookingDoc) => {
  try {
    const eventType = 'TRANSACTION_SUCCESS';
    const result = emailTemplates[`${eventType}`];
    const user = await Client.findOne({ email: transaction.email }).lean();
    if (result) {
      const { template, subject } = result;
      const emailData = { doc: { ...transaction, ...user, ...booking }, template, subject };
      await sendMail(emailData, user);
    } else {
      Logger.error(`Email template not found for the given event type: ${eventType}`);
    }
  } catch (error) {
    Logger.error(`Error sending transaction receipt email: ${error}`);
  }

  return true;
};

export const sendCanceledBookingEmail = async (canceledBooking: BookingDoc[], isAutoCanceled = true) => {
  try {
    // Use different template based on cancellation type
    const eventType = isAutoCanceled ? 'BOOKING_CANCELED' : 'BOOKING_SELF_CANCELED';
    const result = emailTemplates[`${eventType}`];

    for (const booking of canceledBooking) {
      const user = await Client.findOne({ _id: booking.clientId }).lean();
      if (result && user) {
        const { template, subject } = result;

        // For auto-canceled bookings, we keep the array format
        if (isAutoCanceled) {
          const emailData = { doc: { 0: booking, ...user }, template, subject };
          await sendMail(emailData, user);
        } else {
          // For self-canceled bookings, we use direct properties
          const emailData = { doc: { ...booking, ...user }, template, subject };
          await sendMail(emailData, user);
        }
      } else {
        Logger.error(`Email template not found or user not found for booking: ${String(booking._id)}`);
      }
    }
  } catch (error) {
    Logger.error(`Error sending canceled booking email: ${error}`);
  }

  return true;
};

// New function to handle auto-cancellation of pending bookings when a booking is paid
export const cancelConflictingBookings = async (paidBooking: BookingDoc) => {
  try {
    // Find all pending bookings for the same car
    const pendingBookings = await Booking.find({
      car: paidBooking.car,
      _id: { $ne: paidBooking._id }, // Exclude the paid booking
      status: BOOKING_STATUS.PENDING
    });

    if (pendingBookings.length === 0) {
      return; // No pending bookings to check
    }

    // Filter bookings that have date conflicts with the paid booking
    const paidStartDate = new Date(paidBooking.startDate);
    const paidEndDate = new Date(paidBooking.endDate);

    const conflictingBookings = pendingBookings.filter(booking => {
      const bookingStartDate = new Date(booking.startDate);
      const bookingEndDate = new Date(booking.endDate);

      // Check if there's an overlap between the date ranges
      return (
        (bookingStartDate >= paidStartDate && bookingStartDate <= paidEndDate) ||
        (bookingEndDate >= paidStartDate && bookingEndDate <= paidEndDate) ||
        (bookingStartDate <= paidStartDate && bookingEndDate >= paidEndDate)
      );
    });

    if (conflictingBookings.length > 0) {
      // Update all conflicting bookings to canceled
      const bookingIds = conflictingBookings.map(booking => booking._id);

      await Booking.updateMany({ _id: { $in: bookingIds } }, { $set: { status: BOOKING_STATUS.CANCELED } });

      // Send cancellation emails
      await sendCanceledBookingEmail(conflictingBookings, true);

      Logger.info(
        `Auto-canceled ${conflictingBookings.length} conflicting bookings for car: ${String(paidBooking.car)}`
      );
    }
  } catch (error) {
    Logger.error(`Error canceling conflicting bookings: ${error}`);
  }
};

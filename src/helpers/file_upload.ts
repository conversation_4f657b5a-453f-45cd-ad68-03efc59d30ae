import { Agenda } from 'agenda';
import { v2 as cloudinary } from 'cloudinary';
import fs from 'fs';
import multer from 'multer';
import path from 'path';
import { fileURLToPath } from 'url';

import Logger from '../libs/logger.js';
import CarImage from '../models/CarImageModel/CarImageModel.js';
import Car from '../models/CarModel/CarModel.js';
import { CarDoc } from './../types/car.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Enhanced Image Upload Configuration for Car Images
 *
 * File Size: 5MB - Optimal balance between quality and upload speed
 * Supported Formats: PNG, JPG, JPEG, WebP (high-quality formats only)
 *
 * Image Processing:
 * - Main image: 1200x800px (3:2 aspect ratio, standard for car listings)
 * - Medium: 800x600px (4:3 aspect ratio, for gallery views)
 * - Thumbnail: 400x300px (4:3 aspect ratio, for list views)
 *
 * Quality Settings:
 * - Auto quality optimization with 'good' baseline
 * - Progressive JPEG loading for better UX
 * - WebP format delivery for modern browsers
 * - Smart cropping with auto gravity
 */
const FILE_SIZE = 1048576 * 5; // 5MB - Optimal for high-quality images

type JobDoc = {
  fileName: string;
  filePath: string;
  originalname: string;
  data: CarDoc;
  model: string;
};

const storage = multer.diskStorage({
  destination: (_req, file, cb) => {
    cb(null, path.join(__dirname));
  },
  filename: (req, file, cb) => {
    const fileName = file.originalname.toLowerCase().split(' ').join('-');
    cb(null, `${Date.now()}-${fileName}`);
  }
});

export const upload = multer({
  storage: storage,
  limits: {
    fieldNameSize: 300,
    fileSize: FILE_SIZE
  },
  fileFilter: (req, file, cb) => {
    // Only allow high-quality image formats for car images
    const allowedImageTypes = [
      'image/png',
      'image/jpg',
      'image/jpeg',
      'image/webp' // Modern format for better compression
    ];

    if (allowedImageTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(null, false);
      cb(new Error('Only high-quality image formats (PNG, JPG, JPEG, WebP) are allowed for car images!'));
    }

    const fileSize = parseInt(req.headers['content-length']);
    if (fileSize > FILE_SIZE) {
      return cb(new Error('File size too large, should not be larger than 5MB'));
    }
  }
});

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

export const manageFileUpload = async (filePath, fileName, originalname, data) => {
  try {
    const mongoConnectionString = process.env.MONGO_URL;
    if (!mongoConnectionString) {
      throw new Error('MONGO_URL is not defined');
    }

    const agenda = new Agenda({
      db: { address: mongoConnectionString, collection: 'jobCollection' }
    });

    agenda.define('Upload Images', async job => {
      const { filePath, fileName, originalname, data } = job.attrs.data as JobDoc;
      await uploadToCloudinary({ filePath, fileName, originalname, data });
    });

    await agenda.start();

    await agenda.schedule('in 2 seconds', 'Upload Images', {
      filePath,
      fileName,
      data,
      originalname,
      model: 'cars'
    });

    Logger.info('Agenda started and job scheduled');
  } catch (error) {
    console.error('Error:', error);
  }
};

const uploadToCloudinary = async job => {
  const { fileName, filePath, originalname, data } = job as JobDoc;
  try {
    // Enhanced Cloudinary upload configuration for high-quality car images
    const result = await cloudinary.uploader.upload(filePath, {
      public_id: fileName,
      resource_type: 'image',
      // Image optimization settings
      quality: 'auto:good', // Automatic quality optimization with good quality baseline
      format: 'auto', // Automatic format selection (WebP for modern browsers, fallback to original)
      // Transformation for consistent dimensions and quality
      transformation: [
        {
          // Resize to standard car image dimensions while maintaining aspect ratio
          width: 1200,
          height: 800,
          crop: 'fill', // Fill the dimensions, cropping if necessary
          gravity: 'auto', // Smart cropping focusing on the most important part
          quality: 'auto:good',
          fetch_format: 'auto' // Deliver in the best format for the user's browser
        },
        {
          // Additional optimization
          flags: 'progressive', // Progressive JPEG loading
          dpr: 'auto' // Automatic DPR (Device Pixel Ratio) adjustment
        }
      ],
      // Generate multiple sizes for responsive images
      eager: [
        {
          width: 400,
          height: 300,
          crop: 'fill',
          gravity: 'auto',
          quality: 'auto:good',
          format: 'auto'
        },
        {
          width: 800,
          height: 600,
          crop: 'fill',
          gravity: 'auto',
          quality: 'auto:good',
          format: 'auto'
        }
      ],
      // Folder organization
      folder: 'luxlet/car-images',
      // Additional metadata
      context: {
        car_id: data._id.toString(),
        company_id: data.company.toString(),
        upload_type: 'car_image'
      }
    });
    const { asset_id, public_id, signature, format, url, secure_url } = result;

    // Generate responsive image URLs using Cloudinary's transformation API
    const baseUrl = secure_url.split('/upload/')[0] + '/upload/';
    const imagePath = secure_url.split('/upload/')[1];

    const responsiveUrls = {
      thumbnail: `${baseUrl}w_400,h_300,c_fill,g_auto,q_auto:good,f_auto/${imagePath}`,
      medium: `${baseUrl}w_800,h_600,c_fill,g_auto,q_auto:good,f_auto/${imagePath}`,
      large: `${baseUrl}w_1200,h_800,c_fill,g_auto,q_auto:good,f_auto/${imagePath}`
    };

    const carImage = new CarImage({
      asset_id,
      public_id,
      signature,
      format,
      url,
      secure_url,
      responsive_urls: responsiveUrls,
      model: 'cars',
      action: 'car-image-uploads',
      carId: data._id,
      companyId: data.company,
      isMain: originalname == data.mainImageId ? true : undefined,
      createdBy: data.createdBy
    });
    await carImage.save();

    await Car.findOneAndUpdate({ _id: data._id }, { $push: { carImages: carImage._id }, createdBy: data.createdBy });

    fs.unlinkSync(filePath);
  } catch (error) {
    console.error('Error processing job:', error);
  }
};

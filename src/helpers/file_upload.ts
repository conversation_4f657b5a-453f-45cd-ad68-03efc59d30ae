import { Agenda } from 'agenda';
import { v2 as cloudinary } from 'cloudinary';
import fs from 'fs';
import multer from 'multer';
import path from 'path';
import { fileURLToPath } from 'url';

import Logger from '../libs/logger.js';
import CarImage from '../models/CarImageModel/CarImageModel.js';
import Car from '../models/CarModel/CarModel.js';
import { CarDoc } from './../types/car.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const FILE_SIZE = 1048576 * 11; // 11MB

type JobDoc = {
  fileName: string;
  filePath: string;
  originalname: string;
  data: CarDoc;
  model: string;
};

const storage = multer.diskStorage({
  destination: (_req, file, cb) => {
    cb(null, path.join(__dirname));
  },
  filename: (req, file, cb) => {
    const fileName = file.originalname.toLowerCase().split(' ').join('-');
    cb(null, `${Date.now()}-${fileName}`);
  }
});

export const upload = multer({
  storage: storage,
  limits: {
    fieldNameSize: 300,
    fileSize: FILE_SIZE
  },
  fileFilter: (req, file, cb) => {
    if (
      file.mimetype == 'image/png' ||
      file.mimetype == 'application/octet-stream' ||
      file.mimetype == 'image/jpg' ||
      file.mimetype == 'image/jpeg' ||
      file.mimetype == 'image/gif' ||
      file.mimetype == 'video/mp4' ||
      file.mimetype == 'video/webm' ||
      file.mimetype == 'video/ogg'
    ) {
      cb(null, true);
    } else {
      cb(null, false);
      cb(new Error('file format not allowed!'));
    }
    const fileSize = parseInt(req.headers['content-length']);
    if (fileSize > FILE_SIZE) {
      return cb(new Error('File size too large, should not be larger than 11MB'));
    }
  }
});

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

export const manageFileUpload = async (filePath, fileName, originalname, data) => {
  try {
    const mongoConnectionString = process.env.MONGO_URL;
    if (!mongoConnectionString) {
      throw new Error('MONGO_URL is not defined');
    }

    const agenda = new Agenda({
      db: { address: mongoConnectionString, collection: 'jobCollection' }
    });

    agenda.define('Upload Images', async job => {
      const { filePath, fileName, originalname, data } = job.attrs.data as JobDoc;
      await uploadToCloudinary({ filePath, fileName, originalname, data });
    });

    await agenda.start();

    await agenda.schedule('in 2 seconds', 'Upload Images', {
      filePath,
      fileName,
      data,
      originalname,
      model: 'cars'
    });

    Logger.info('Agenda started and job scheduled');
  } catch (error) {
    console.error('Error:', error);
  }
};

const uploadToCloudinary = async job => {
  const { fileName, filePath, originalname, data } = job as JobDoc;
  try {
    const result = await cloudinary.uploader.upload(filePath, {
      public_id: fileName,
      resource_type: 'auto'
    });
    const { asset_id, public_id, signature, format, url, secure_url } = result;

    const carImage = new CarImage({
      asset_id,
      public_id,
      signature,
      format,
      url,
      secure_url,
      model: 'cars',
      action: 'car-image-uploads',
      carId: data._id,
      companyId: data.company,
      isMain: originalname == data.mainImageId ? true : undefined,
      createdBy: data.createdBy
    });
    await carImage.save();

    await Car.findOneAndUpdate({ _id: data._id }, { $push: { carImages: carImage._id }, createdBy: data.createdBy });

    fs.unlinkSync(filePath);
  } catch (error) {
    console.error('Error processing job:', error);
  }
};

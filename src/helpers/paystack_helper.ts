import axios from 'axios';

import Booking from '../models/BookingModel/BookingModel.js';
import Car from '../models/CarModel/CarModel.js';
import Mandate from '../models/TransactionModel/MandateModel.js';
import Transaction from '../models/TransactionModel/TransactionModel.js';
import { BOOKING_STATUS } from '../types/booking.js';
import { VerifyDoc } from '../types/paystack.js';
import { TRANSACTION_STATUS, TransactionDoc } from '../types/transactions.js';
import { sendCanceledBookingEmail, sendTransactionReceiptEmail } from './booking_helper.js';

const headers = {
  'Content-Type': 'application/json',
  Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`
};

export const initializePaystackTransaction = async (transaction: TransactionDoc) => {
  const { amount, email, currency } = transaction;

  const data = JSON.stringify({
    amount: String(amount),
    email,
    currency
  });

  const config = {
    method: 'post',
    url: `${process.env.PAYSTACK_BASE_URL}/transaction/initialize`,
    headers,
    data
  };

  let response = {} as { data: unknown };
  try {
    response = await axios.request(config);
    if (!response || !response.data) return null;

    return response.data;
  } catch (error: unknown) {
    return error;
  }
};

export const verifyPaystackTransaction = async (reference: string) => {
  let response: { data: { data: unknown } };
  const config = {
    method: 'get',
    url: `${process.env.PAYSTACK_BASE_URL}/transaction/verify/${reference}`,
    headers
  };

  try {
    response = await axios.request(config);
    if (!response || !response?.data) return null;

    return response.data.data;
  } catch (error: unknown) {
    return error;
  }
};

export const paystackChargeMandate = async (transaction: TransactionDoc) => {
  const { auth, amount, email, reference } = transaction;
  let response: { data: { data: unknown } };
  const data = JSON.stringify({
    email: email,
    amount: String(amount),
    reference: reference,
    authorization_code: auth
  });

  const config = {
    method: 'post',
    url: `${process.env.PAYSTACK_BASE_URL}/transaction/charge_authorization`,
    headers,
    data
  };

  try {
    response = await axios.request(config);
    if (!response || !response.data) return null;
    return response.data.data;
  } catch (error) {
    return null;
  }
};

export const processPaystackTransaction = async (
  transaction: TransactionDoc,
  verify_: VerifyDoc,
  reference: string
) => {
  if (verify_) {
    transaction.channel = verify_.channel;
    transaction.message = verify_.message;
    if (verify_.status === 'success') {
      if (verify_.authorization) {
        const {
          authorization_code,
          bin,
          last4,
          exp_month,
          exp_year,
          channel,
          card_type,
          bank,
          country_code,
          brand,
          reusable,
          signature,
          account_name
        } = verify_.authorization;

        if (channel && channel === 'card') {
          let mandate = await Mandate.findOne({
            $and: [{ signature: signature }, { clientId: transaction.clientId }]
          });

          if (mandate) {
            mandate.authorization = authorization_code;
          } else
            mandate = new Mandate({
              clientId: transaction.clientId,
              authorization: authorization_code,
              accountName: account_name,
              expiryMonth: exp_month,
              expiryYear: exp_year,
              countryCode: country_code,
              cardType: card_type,
              signature: signature,
              reusable: reusable,
              channel,
              brand,
              bank,
              bin,
              last4,
              body: verify_.authorization
            });
          await mandate.save();
        }
      }

      transaction.status = TRANSACTION_STATUS.SUCCESS;
      transaction.charge = parseInt(verify_.fees) / 100;
      const updatedTransaction = await processTransaction(transaction, reference);
      return {
        status: 'success',
        updatedTransaction
      };
    } else {
      const updatedTransaction = await Transaction.updateOne(
        { _id: transaction._id },
        { status: TRANSACTION_STATUS.FAILED }
      );
      return {
        status: 'failed',
        updatedTransaction
      };
    }
  }
};

/* export const processTransactionHook = async (transaction: TransactionDoc) => {
  const mongoConnectionString = process.env.MONGODB_URL;
  if (!mongoConnectionString) {
    throw new Error('MONGODB_URL is not defined');
  }

  const agenda = new Agenda({
    db: { address: mongoConnectionString, collection: 'jobCollection' }
  });

  agenda.define('process paystack hook', async job => {
    const transaction = job.attrs.data as TransactionDoc;
    await processTransaction(transaction, reference);
  });

  await agenda.start();
  await agenda.schedule('in 2 seconds', 'process paystack hook', transaction);
}; */

const processTransaction = async (transaction: TransactionDoc, reference: string) => {
  const booking = await Booking.findOne({ _id: transaction.bookingId });
  const updatedTransaction = await Transaction.findByIdAndUpdate(
    { _id: transaction._id },
    { status: TRANSACTION_STATUS.SUCCESS, reference },
    { new: true }
  );

  //Ensure that any valid booking for this car is canceled and sent a mail
  const allBookingsAvailable = await Booking.find({ status: BOOKING_STATUS.PENDING, car: booking.carId });
  if (allBookingsAvailable?.length > 1) {
    const bookingsToUpdate = allBookingsAvailable.filter(booking => booking.bookingReference !== transaction.reference);
    const ids = bookingsToUpdate.map(bk => bk._id);
    /* const canceledBooking = await Booking.updateMany(
      {
        _id: { $in: ids },
        status: BOOKING_STATUS.IN_PROGRESS
      },
      {
        $set: { status: BOOKING_STATUS.CANCELED }
      },
      { new: true }
    ); */

    await Booking.updateMany(
      {
        _id: { $in: ids },
        status: BOOKING_STATUS.IN_PROGRESS
      },
      {
        $set: { status: BOOKING_STATUS.CANCELED }
      }
    );

    const canceledBookings = await Booking.find({
      _id: { $in: ids },
      status: BOOKING_STATUS.CANCELED
    });
    if (canceledBookings.length) {
      await sendCanceledBookingEmail(canceledBookings);
    }
  }

  const updatedBooking = await Booking.findByIdAndUpdate(
    { _id: booking._id },
    {
      status: BOOKING_STATUS.BOOKED
    },
    { new: true }
  );

  const { startDate, endDate, startTime } = updatedBooking;

  await Car.findByIdAndUpdate(
    { _id: updatedBooking.car },
    {
      $push: {
        bookedDates: {
          startDate,
          endDate,
          startTime
        }
      }
    }
  );

  await sendTransactionReceiptEmail(updatedTransaction.toObject(), updatedBooking.toObject());
  return updatedTransaction;
};

export const generatePaystackReference = (char: number) => {
  const characters = '0123456789';
  let reference = '';
  for (let i = 0; i < char; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    reference += characters[randomIndex];
  }

  return reference;
};

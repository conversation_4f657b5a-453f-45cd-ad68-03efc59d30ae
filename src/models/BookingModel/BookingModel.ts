import mongoose, { Document, Model, Query, Schema } from 'mongoose';

import { calculateDaysBetween } from '../../helpers/application_helper.js';
import { calculateBookingCharges, calculateDistance } from '../../helpers/car_helper.js';
import { APPROVAL_STATUS, BOOKING_STATUS, BOOKING_TYPE, BookingDoc } from '../../types/booking.js';
import Car from '../CarModel/CarModel.js';
import { addressSchema } from '../CompanyModel/CompanyModel.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';
import { bookingsAfterSave, bookingsAfterUpdate } from './booking_after_save.js';

type BaseDocument<T> = {
  _doc: T;
};

export type BookingDocumentResult = BookingDoc & BaseDocument<BookingDoc>;

type BookingModel = BaseModelMethods<BookingDocumentResult> & Model<BookingDoc>;

const bookingSchema = new mongoose.Schema<BookingDocumentResult, BookingModel>(
  {
    clientId: {
      type: Schema.Types.ObjectId,
      ref: 'Client',
      required: true
    },
    companyId: {
      type: Schema.Types.ObjectId,
      ref: 'Company',
      required: true
    },
    managerId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    car: {
      type: Schema.Types.ObjectId,
      ref: 'Car',
      required: true
    },
    startDate: {
      type: String,
      required: true
    },
    startTime: {
      type: String,
      required: true
    },
    endDate: {
      type: String,
      required: true
    },
    status: {
      type: String,
      enum: BOOKING_STATUS,
      required: true
    },
    approvalStatus: {
      type: String,
      enum: APPROVAL_STATUS,
      required: true,
      default: undefined
    },
    bookingType: {
      type: String,
      enum: BOOKING_TYPE,
      required: true
    },
    pickupAddress: {
      type: addressSchema,
      required: true
    },
    destinationAddress: {
      type: addressSchema,
      required: true
    },
    totalPrice: {
      type: Number
    },
    managerTotal: {
      type: Number
    },
    escortsTotal: {
      type: Number
    },
    hasAdditionalStop: {
      type: Boolean
    },
    vat: {
      type: Number
    },
    numberOfDays: {
      // this should be generated from startDate and endDate.
      type: Number
    },
    escortCount: {
      type: Number
    },
    escortDays: {
      type: Number
    },
    pickupKilometer: {
      type: Number
    },
    destinationKilometer: {
      type: Number
    },
    bookingCharges: {
      type: Number
    },
    bookingReference: {
      type: String
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

bookingSchema.static('findOneActive', findOneActive);
bookingSchema.static('findActive', findActive);
bookingSchema.static('findAndPopulate', findAndPopulate);
bookingSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

bookingSchema.pre('save', async function (next) {
  const car = await Car.findOneActive({ _id: this.car });
  const numberOfDays = calculateDaysBetween(this.startDate, this.endDate);
  this.numberOfDays = numberOfDays;

  const pickupKilometer = calculateDistance(car.address, this.pickupAddress);
  this.pickupKilometer = pickupKilometer;
  const destinationKilometer = calculateDistance(this.pickupAddress, this.destinationAddress);
  this.destinationKilometer = destinationKilometer;

  const bookingCharges = calculateBookingCharges(car, this);
  const { total, managerTotal, escortsTotal, vat } = bookingCharges;
  this.totalPrice = total;
  this.managerTotal = managerTotal;
  this.escortsTotal = escortsTotal;
  this.vat = vat;

  next();
});

bookingSchema.post('save', async function (doc) {
  await bookingsAfterSave(doc);
});

type BookingQuery = {
  _oldDoc?: Document<BookingDoc>;
} & Query<any, BookingDoc>;

bookingSchema.pre('findOneAndUpdate', async function (this: BookingQuery, next) {
  const query = this.getQuery();
  this._oldDoc = await this.model.findOne(query);
  next();
});

bookingSchema.post('findOneAndUpdate', async function (this: BookingQuery, doc: BookingDoc) {
  const oldDoc = this._oldDoc?.toObject() as BookingDoc;
  await bookingsAfterUpdate(oldDoc, doc);
});

const Booking = mongoose.model<BookingDocumentResult, BookingModel>('Booking', bookingSchema);
export default Booking;

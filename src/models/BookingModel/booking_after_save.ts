import { sendNotification } from '../../helpers/notification_helper.js';
import { sendWhatsAppMessage } from '../../helpers/user_helper.js';
import Logger from '../../libs/logger.js';
import { BOOKING_STATUS, BookingDoc } from '../../types/booking.js';
import { TRANSACTION_STATUS } from '../../types/transactions.js';
import Car from '../CarModel/CarModel.js';
import Client from '../ClientModel/ClientModel.js';
import Transaction from '../TransactionModel/TransactionModel.js';
import User from '../UserModel/UserModel.js';

/**
 * Enhanced booking after save hook with robust error handling and validation
 */
export const bookingAfterSave = async (booking: BookingDoc) => {
  try {
    Logger.info(`Processing bookingAfterSave for booking: ${String(booking._id)} with status: ${booking.status}`);

    // Only send notification if the booking status is PENDING
    if (booking.status === BOOKING_STATUS.PENDING) {
      const { clientId, car } = booking;

      // Validate required data exists
      if (!clientId || !car) {
        Logger.error(`Missing required data for booking ${String(booking._id)}: clientId=${clientId}, car=${car}`);
        return;
      }

      // Fetch client and car data with error handling
      const [client, carBooked] = await Promise.allSettled([
        Client.findOne({ _id: clientId }),
        Car.findOne({ _id: car })
      ]);

      // Check if client fetch was successful
      if (client.status === 'rejected') {
        Logger.error(`Failed to fetch client ${String(clientId)}: ${client.reason}`);
        return;
      }

      // Check if car fetch was successful
      if (carBooked.status === 'rejected') {
        Logger.error(`Failed to fetch car ${String(car)}: ${carBooked.reason}`);
        return;
      }

      const clientData = client.value;
      const carData = carBooked.value;

      // Validate fetched data
      if (!clientData) {
        Logger.error(`Client not found for booking ${String(booking._id)}: ${String(clientId)}`);
        return;
      }

      if (!carData) {
        Logger.error(`Car not found for booking ${String(booking._id)}: ${String(car)}`);
        return;
      }

      // Send notification with proper error handling
      try {
        if (clientData.fcmToken) {
          Logger.info(`Sending booking creation notification to client: ${String(clientId)}`);
          await sendNotification(
            clientData.fcmToken,
            'Your booking has been created',
            `Booking created for ${carData.model}`,
            'BOOKING_CREATED',
            String(clientId),
            undefined
          );
          Logger.info(`Successfully sent booking creation notification to client: ${String(clientId)}`);
        } else {
          Logger.warn(`Client ${String(clientId)} does not have an FCM token. Notification not sent.`);
        }
      } catch (notificationError) {
        Logger.error(`Failed to send notification to client ${String(clientId)}: ${notificationError}`);
        // Don't throw here - notification failure shouldn't break the booking process
      }
    } else {
      Logger.info(`Booking ${String(booking._id)} status is ${booking.status}, no notification needed`);
    }
  } catch (error) {
    Logger.error(`Error in bookingAfterSave for booking ${String(booking._id)}: ${error}`);
    // Don't throw here - hook failures shouldn't break the main operation
  }
};

/**
 * Enhanced booking after update hook with comprehensive error handling,
 * validation, and optimized database queries
 */
export const bookingAfterUpdate = async (oldDoc: BookingDoc, newDoc: BookingDoc) => {
  try {
    Logger.info(
      `Processing bookingAfterUpdate for booking: ${String(newDoc._id)} ` +
        `(status changed from ${oldDoc?.status} to ${newDoc.status})`
    );

    // Validate required data
    const { clientId, car, status, managerId } = newDoc;
    if (!clientId || !car) {
      Logger.error(`Missing required data for booking ${String(newDoc._id)}: clientId=${clientId}, car=${car}`);
      return;
    }

    // Fetch all required data in parallel with error handling
    const [clientResult, carResult, transactionResult, managerResult] = await Promise.allSettled([
      Client.findOne({ _id: clientId }),
      Car.findOne({ _id: car }),
      Transaction.findOne({ bookingId: newDoc._id }),
      managerId ? User.findOne({ _id: managerId }) : Promise.resolve(null)
    ]);

    // Extract successful results
    const client = clientResult.status === 'fulfilled' ? clientResult.value : null;
    const carBooked = carResult.status === 'fulfilled' ? carResult.value : null;
    const transaction = transactionResult.status === 'fulfilled' ? transactionResult.value : null;
    const manager = managerResult.status === 'fulfilled' ? managerResult.value : null;

    // Log any fetch failures
    if (clientResult.status === 'rejected') {
      Logger.error(`Failed to fetch client ${String(clientId)}: ${clientResult.reason}`);
    }
    if (carResult.status === 'rejected') {
      Logger.error(`Failed to fetch car ${String(car)}: ${carResult.reason}`);
    }
    if (transactionResult.status === 'rejected') {
      Logger.error(`Failed to fetch transaction for booking ${String(newDoc._id)}: ${transactionResult.reason}`);
    }
    if (managerResult.status === 'rejected') {
      Logger.error(`Failed to fetch manager ${String(managerId)}: ${managerResult.reason}`);
    }

    // Validate essential data
    if (!client) {
      Logger.error(`Client not found for booking ${String(newDoc._id)}: ${String(clientId)}`);
      return;
    }

    if (!carBooked) {
      Logger.error(`Car not found for booking ${String(newDoc._id)}: ${String(car)}`);
      return;
    }

    // Handle status change to BOOKED
    if (oldDoc?.status !== status && status === BOOKING_STATUS.BOOKED) {
      await handleBookingStatusChange(client, carBooked, newDoc);
    }

    // Handle successful payment notifications
    if (transaction && transaction.status === TRANSACTION_STATUS.SUCCESS) {
      await handleSuccessfulPayment(client, carBooked, manager, newDoc);
    }
  } catch (error) {
    Logger.error(`Error in bookingAfterUpdate for booking ${String(newDoc._id)}: ${error}`);
    // Don't throw here - hook failures shouldn't break the main operation
  }
};

/**
 * Handle notifications when booking status changes to BOOKED
 */
const handleBookingStatusChange = async (client: any, carBooked: any, _booking: BookingDoc) => {
  try {
    const message = `Your payment for ${carBooked.model} has been received`;
    Logger.info(`Sending booking confirmation notification to client: ${String(client._id)}`);

    // Send push notification
    if (client.fcmToken) {
      try {
        await sendNotification(
          client.fcmToken,
          'Your payment was received',
          `Payment confirmed for ${carBooked.model}`,
          'BOOKED',
          undefined,
          String(client._id)
        );
        Logger.info(`Successfully sent booking confirmation notification to client: ${String(client._id)}`);
      } catch (notificationError) {
        Logger.error(`Failed to send push notification to client ${String(client._id)}: ${notificationError}`);
      }
    } else {
      Logger.warn(`Client ${String(client._id)} does not have an FCM token for booking confirmation`);
    }

    // Send WhatsApp message
    if (client.phoneNumber) {
      try {
        await sendWhatsAppMessage(client.phoneNumber, message);
        Logger.info(`Successfully sent WhatsApp message to client: ${client.phoneNumber}`);
      } catch (whatsappError) {
        Logger.error(`Failed to send WhatsApp message to client ${client.phoneNumber}: ${whatsappError}`);
      }
    } else {
      Logger.warn(`Client ${String(client._id)} does not have a phone number for WhatsApp`);
    }
  } catch (error) {
    Logger.error(`Error in handleBookingStatusChange: ${error}`);
  }
};

/**
 * Handle notifications when payment is successful
 */
const handleSuccessfulPayment = async (client: any, carBooked: any, manager: any, booking: BookingDoc) => {
  try {
    const message = `Booking Successfully Paid for ${carBooked.model}`;
    Logger.info(`Processing successful payment notifications for booking: ${String(booking._id)}`);

    // Send notification to manager
    if (manager) {
      if (manager.fcmToken) {
        try {
          await sendNotification(
            manager.fcmToken,
            'Your car has been booked',
            `Payment received for ${carBooked.model}`,
            'SUCCESSFUL_PAYMENT',
            String(manager._id),
            undefined
          );
          Logger.info(`Successfully sent payment notification to manager: ${String(manager._id)}`);
        } catch (notificationError) {
          Logger.error(`Failed to send push notification to manager ${String(manager._id)}: ${notificationError}`);
        }
      } else {
        Logger.warn(`Manager ${String(manager._id)} does not have an FCM token`);
      }

      // Send WhatsApp to manager
      if (manager.phoneNumber) {
        try {
          await sendWhatsAppMessage(manager.phoneNumber, message);
          Logger.info(`Successfully sent WhatsApp message to manager: ${manager.phoneNumber}`);
        } catch (whatsappError) {
          Logger.error(`Failed to send WhatsApp message to manager ${manager.phoneNumber}: ${whatsappError}`);
        }
      } else {
        Logger.warn(`Manager ${String(manager._id)} does not have a phone number`);
      }
    } else {
      Logger.warn(`Manager not found for booking ${String(booking._id)}`);
    }

    // Send WhatsApp to client
    if (client.phoneNumber) {
      try {
        await sendWhatsAppMessage(client.phoneNumber, message);
        Logger.info(`Successfully sent payment WhatsApp message to client: ${client.phoneNumber}`);
      } catch (whatsappError) {
        Logger.error(`Failed to send payment WhatsApp message to client ${client.phoneNumber}: ${whatsappError}`);
      }
    } else {
      Logger.warn(`Client ${String(client._id)} does not have a phone number for payment notification`);
    }
  } catch (error) {
    Logger.error(`Error in handleSuccessfulPayment: ${error}`);
  }
};

// export const bookingsAfterSave = async (booking: BookingDoc) => {
//   const { clientId, car, status, _id } = booking;
//   const client = await Client.findOne({ _id: clientId });
//   const carBooked = await Car.findOne({ _id: car });
//   const transaction = await Transaction.findOne({ bookingId: _id });
//   const user = await User.findOne({ _id: carBooked.createdBy });

//   try {
//     // SENDING NOTIFICATION TO CLIENT WHEN BOOKING IS CREATED
//     if (status == BOOKING_STATUS.PENDING) {
//       Logger.info('SENDING NOTIFICATION TO CLIENT WHEN BOOKING IS CREATED');
//       return await sendNotification(
//         client?.fcmToken,
//         'Your booking has been created',
//         `Booking created for ${carBooked.model}`,
//         'BOOKING_CREATED',
//         clientId.toString(),
//         undefined
//       );
//     }

//     // SENDING NOTIFICATION TO CLIENT WHEN CAR AS BEEN BOOKED
//     if (status == BOOKING_STATUS.BOOKED) {
//       const message = `Your Car ${carBooked.model} as been Booked`;
//       Logger.info('SENDING NOTIFICATION TO CLIENT WHEN CAR AS BEEN BOOKED');
//       await sendNotification(
//         client?.fcmToken,
//         'Your Car has been Booked',
//         `Booking created for ${carBooked.model}`,
//         'BOOKED',
//         clientId.toString(),
//         undefined
//       );
//       await sendWhatsAppMessage(client.phoneNumber, message);
//     }

//     // SENDING NOTIFICATION TO CLIENT & MANAGER WHEN BOOKING AS BEEN PAID
//     if (transaction.status == TRANSACTION_STATUS.SUCCESS) {
//       const message = `Booking Successfully Paid for ${carBooked.model}`;
//       const manager = await User.findOne({ _id: booking.managerId });
//       Logger.info('SENDING NOTIFICATION TO CLIENT & MANAGER WHEN BOOKING AS BEEN PAID');
//       await sendNotification(
//         client?.fcmToken,
//         'Payment Successful',
//         `Booking successfully paid for ${carBooked.model}`,
//         'SUCCESSFUL_PAYMENT',
//         clientId.toString(),
//         user._id.toString()
//       );
//       await sendWhatsAppMessage(client.phoneNumber, message);
//       await sendWhatsAppMessage(manager.phoneNumber, message);
//     }
//   } catch (error) {
//     console.error(error);
//   }

//   return true;
// };

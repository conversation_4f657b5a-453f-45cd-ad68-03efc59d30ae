import { sendNotification } from '../../helpers/notification_helper.js';
import { sendWhatsAppMessage } from '../../helpers/user_helper.js';
import Logger from '../../libs/logger.js';
import { BOOKING_STATUS, BookingDoc } from '../../types/booking.js';
import { TRANSACTION_STATUS } from '../../types/transactions.js';
import Car from '../CarModel/CarModel.js';
import Client from '../ClientModel/ClientModel.js';
import Transaction from '../TransactionModel/TransactionModel.js';
import User from '../UserModel/UserModel.js';

export const bookingsAfterSave = async (booking: BookingDoc) => {
  // Only send notification if the booking status is PENDING
  if (booking.status === BOOKING_STATUS.PENDING) {
    const { clientId, car } = booking;
    const client = await Client.findOne({ _id: clientId });
    const carBooked = await Car.findOne({ _id: car });

    try {
      Logger.info('SENDING NOTIFICATION TO CLIENT WHEN BOOKING IS CREATED');
      await sendNotification(
        client?.fcmToken,
        'Your booking has been created',
        `Booking created for ${carBooked.model}`,
        'BOOKING_CREATED',
        String(clientId),
        undefined
      );
    } catch (error) {
      Logger.error(`Error sending notification to client when booking is created: ${error}`);
    }
  }
};

export const bookingsAfterUpdate = async (oldDoc: BookingDoc, newDoc: BookingDoc) => {
  const { clientId, car, status } = newDoc;
  const client = await Client.findOne({ _id: clientId });
  const carBooked = await Car.findOne({ _id: car });
  const transaction = await Transaction.findOne({ bookingId: newDoc._id });

  try {
    // SENDING NOTIFICATION TO CLIENT WHEN CAR HAS BEEN BOOKED
    if (oldDoc.status !== status && status === BOOKING_STATUS.BOOKED) {
      const message = `Your payment for ${carBooked.model} has been received`;
      Logger.info('SENDING NOTIFICATION TO CLIENT WHEN CAR HAS BEEN BOOKED');
      await sendNotification(
        client?.fcmToken,
        'Your payment was received',
        `Booking created for ${carBooked.model}`,
        'BOOKED',
        undefined,
        String(clientId)
      );
      await sendWhatsAppMessage(client.phoneNumber, message);
    }

    // SENDING PUSH NOTIFICATION TO MANAGER & WHATSAPP MESSAGE TO CLIENT & MANAGER WHEN BOOKING HAS BEEN PAID
    if (transaction && transaction.status === TRANSACTION_STATUS.SUCCESS) {
      const message = `Booking Successfully Paid for ${carBooked.model}`;
      const manager = await User.findOne({ _id: newDoc.managerId });

      // Add logging to debug manager FCM token
      Logger.info(
        'SENDING PUSH NOTIFICATION TO MANAGER & WHATSAPP MESSAGE TO CLIENT & MANAGER WHEN BOOKING HAS BEEN PAID'
      );

      // Check if manager has a valid FCM token
      if (!manager?.fcmToken) {
        Logger.error(`Manager (${String(manager?._id)}) does not have a valid FCM token. Notification not sent.`);
      } else {
        try {
          await sendNotification(
            manager.fcmToken,
            'Your car has been booked',
            `Payment received for ${carBooked.model}`,
            'SUCCESSFUL_PAYMENT',
            String(manager._id),
            undefined
          );
        } catch (error) {
          Logger.error(`Failed to send push notification to manager: ${error}`);
        }
      }

      // Send WhatsApp messages separately to ensure they're sent even if push notification fails
      try {
        await sendWhatsAppMessage(client.phoneNumber, message);
      } catch (error) {
        Logger.error(`Failed to send WhatsApp message to client: ${error}`);
      }

      try {
        await sendWhatsAppMessage(manager.phoneNumber, message);
      } catch (error) {
        Logger.error(`Failed to send WhatsApp message to manager: ${error}`);
      }
    }
  } catch (error) {
    Logger.error(`Error sending notification when booking is paid for: ${error}`);
  }
};

// export const bookingsAfterSave = async (booking: BookingDoc) => {
//   const { clientId, car, status, _id } = booking;
//   const client = await Client.findOne({ _id: clientId });
//   const carBooked = await Car.findOne({ _id: car });
//   const transaction = await Transaction.findOne({ bookingId: _id });
//   const user = await User.findOne({ _id: carBooked.createdBy });

//   try {
//     // SENDING NOTIFICATION TO CLIENT WHEN BOOKING IS CREATED
//     if (status == BOOKING_STATUS.PENDING) {
//       Logger.info('SENDING NOTIFICATION TO CLIENT WHEN BOOKING IS CREATED');
//       return await sendNotification(
//         client?.fcmToken,
//         'Your booking has been created',
//         `Booking created for ${carBooked.model}`,
//         'BOOKING_CREATED',
//         clientId.toString(),
//         undefined
//       );
//     }

//     // SENDING NOTIFICATION TO CLIENT WHEN CAR AS BEEN BOOKED
//     if (status == BOOKING_STATUS.BOOKED) {
//       const message = `Your Car ${carBooked.model} as been Booked`;
//       Logger.info('SENDING NOTIFICATION TO CLIENT WHEN CAR AS BEEN BOOKED');
//       await sendNotification(
//         client?.fcmToken,
//         'Your Car has been Booked',
//         `Booking created for ${carBooked.model}`,
//         'BOOKED',
//         clientId.toString(),
//         undefined
//       );
//       await sendWhatsAppMessage(client.phoneNumber, message);
//     }

//     // SENDING NOTIFICATION TO CLIENT & MANAGER WHEN BOOKING AS BEEN PAID
//     if (transaction.status == TRANSACTION_STATUS.SUCCESS) {
//       const message = `Booking Successfully Paid for ${carBooked.model}`;
//       const manager = await User.findOne({ _id: booking.managerId });
//       Logger.info('SENDING NOTIFICATION TO CLIENT & MANAGER WHEN BOOKING AS BEEN PAID');
//       await sendNotification(
//         client?.fcmToken,
//         'Payment Successful',
//         `Booking successfully paid for ${carBooked.model}`,
//         'SUCCESSFUL_PAYMENT',
//         clientId.toString(),
//         user._id.toString()
//       );
//       await sendWhatsAppMessage(client.phoneNumber, message);
//       await sendWhatsAppMessage(manager.phoneNumber, message);
//     }
//   } catch (error) {
//     console.error(error);
//   }

//   return true;
// };

import mongoose, { Model } from 'mongoose';

import { CarCategoryDoc } from '../../types/car_category.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type CarCategoryDocumentResult = CarCategoryDoc & BaseDocument<CarCategoryDoc>;

type CarCategoryModel = BaseModelMethods<CarCategoryDocumentResult> & Model<CarCategoryDoc>;

const carCategorySchema = new mongoose.Schema<CarCategoryDocumentResult, CarCategoryModel>(
  {
    name: {
      type: String,
      required: true,
      unique: true
    },
    pictureUrl: {
      type: String,
      required: true,
      unique: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

carCategorySchema.static('findOneActive', findOneActive);
carCategorySchema.static('findActive', findActive);
carCategorySchema.static('findAndPopulate', findAndPopulate);
carCategorySchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const CarCategory = mongoose.model<CarCategoryDocumentResult, CarCategoryModel>('CarCategory', carCategorySchema);
export default CarCategory;

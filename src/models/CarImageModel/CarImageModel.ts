import mongoose, { Model, Schema } from 'mongoose';

import { CarImageDoc } from '../../types/carImage.js';
import {
  BaseModelMethods,
  findActive,
  findAllActiveAndPopulate,
  findAndPopulate,
  findOneActive
} from '../methods/methods.js';

type BaseDocument<T> = {
  _doc: T;
};

export type CarImageDocumentResult = CarImageDoc & BaseDocument<CarImageDoc>;

type CarImageModel = BaseModelMethods<CarImageDocumentResult> & Model<CarImageDoc>;

const carImageSchema = new mongoose.Schema<CarImageDocumentResult, CarImageModel>(
  {
    action: {
      type: String,
      required: true
    },
    asset_id: {
      type: String,
      required: true
    },
    public_id: {
      type: String,
      required: true
    },
    signature: {
      type: String,
      required: true
    },
    format: {
      type: String,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    secure_url: {
      type: String,
      required: true
    },
    model: {
      type: String,
      required: true
    },
    isMain: {
      type: Boolean
    },
    carId: {
      type: Schema.Types.ObjectId,
      ref: 'Car',
      required: true
    },
    companyId: {
      type: Schema.Types.ObjectId,
      ref: 'Coompany',
      required: true
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    isVisible: {
      type: Boolean,
      default: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

carImageSchema.static('findOneActive', findOneActive);
carImageSchema.static('findActive', findActive);
carImageSchema.static('findAndPopulate', findAndPopulate);
carImageSchema.static('findAllActiveAndPopulate', findAllActiveAndPopulate);

const CarImage = mongoose.model<CarImageDocumentResult, CarImageModel>('CarImage', carImageSchema);
export default CarImage;

import express from 'express';

import {
  createCar,
  createCategory,
  deleteCarCategory,
  deleteCarImages,
  delistCar,
  fetchCarsForClient,
  getBrandsAndCategories,
  getCar,
  getCars,
  updateCar,
  updateCarCategory,
  updateImageVisibility,
  updateMainImage,
  uploadCarImages
} from '../controllers/car_controller.js';
import { upload } from '../helpers/file_upload.js';
import { authorize, authorizeClient, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// Create a category
router.post('/category', authorize, restrictToRoles(['admin']), createCategory);

// Update a category
router.put('/category/:id', authorize, restrictToRoles(['admin']), updateCarCategory);

// Delete a category
router.delete('/category/:id', authorize, restrictToRoles(['admin']), deleteCarCategory);

// Get brands and categories
router.get('/brands-and-categories', getBrandsAndCategories);

// Fetch available categories for client
router.get('/categories/get', fetchCarsForClient);

// Upload car images
router.post('/images', authorize, restrictToRoles(['admin', 'manager']), upload.array('images'), uploadCarImages);

// Update main image for a car
router.patch('/images/:id', authorize, restrictToRoles(['admin', 'manager']), updateMainImage);

// Update image visibility
router.put('/images', authorize, restrictToRoles(['manager', 'admin']), updateImageVisibility);

// Delete a specific car image
router.delete('/images/:id', authorize, restrictToRoles(['admin', 'manager']), deleteCarImages);

// Create a car
router.post('/', authorize, restrictToRoles(['admin', 'manager']), createCar);

// Fetch a car for client (client-specific view)
router.get('/:id/client', authorizeClient, getCar);

// Fetch a car by ID (admin/manager view)
router.get('/:id', authorize, restrictToRoles(['manager', 'admin']), getCar);

// Fetch all cars
router.get('/', authorize, restrictToRoles(['manager', 'admin']), getCars);

// Update a car
router.patch('/:id', authorize, restrictToRoles(['manager', 'admin']), updateCar);

// Delete (delist) a car
router.delete('/:id', authorize, restrictToRoles(['admin', 'manager']), delistCar);

export default router;

import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';

export type CarImageDoc = {
  _id: Types.ObjectId;
  action: string;
  asset_id: string;
  public_id: string;
  signature: string;
  format: string;
  url: string;
  secure_url: string;
  model: string;
  isMain: boolean;
  carId: Types.ObjectId;
  companyId: Types.ObjectId;
  isVisible: boolean;
  isActive: boolean;
  createdAt: Date;
  createdBy: Types.ObjectId;
  updatedBy: Types.ObjectId;
  updatedAt: Date;
};

export type AdvancedCarImageQueryResult = AdvancedQueryResult<CarImageDoc>;

export type RegisterCarImageRequestBody = Omit<CarImageDoc, '_id' | 'createdAt' | 'updatedAt'>;

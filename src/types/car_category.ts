import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';

export type CarCategoryDoc = {
  _id: Types.ObjectId;
  id: string;
  name: string;
  pictureUrl: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type AdvancedUsersQueryResult = AdvancedQueryResult<CarCategoryDoc>;

export type RegisterUserRequestBody = Omit<CarCategoryDoc, '_id' | 'createdAt' | 'updatedAt'>;

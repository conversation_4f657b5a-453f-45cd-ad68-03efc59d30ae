import { TRANSACTION_CHANEL } from './transactions.js';

export type PaystackInitDocs = {
  status: boolean;
  message: string;
  data: {
    authorization_url: string;
    access_code: string;
    reference: string;
  };
};

export type AuthorizationDoc = {
  authorization_code: string;
  bin: string;
  last4: string;
  exp_month: string;
  exp_year: string;
  channel: string;
  card_type: string;
  bank: string;
  country_code: string;
  brand: string;
  reusable: string;
  signature: string;
  account_name: string;
};

export type VerifyDoc = {
  channel: TRANSACTION_CHANEL;
  message: string;
  fees: string;
  amount: string;
  status: string;
  authorization: AuthorizationDoc;
};
